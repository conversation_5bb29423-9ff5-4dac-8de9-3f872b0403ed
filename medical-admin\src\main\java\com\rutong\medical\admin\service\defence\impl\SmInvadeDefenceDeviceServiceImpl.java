package com.rutong.medical.admin.service.defence.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.entity.defence.SmInvadeDefenceDevice;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.mapper.defence.SmInvadeDefenceDeviceMapper;
import com.rutong.medical.admin.mapper.device.DeviceMapper;
import com.rutong.medical.admin.service.defence.SmInvadeDefenceDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-19
 */
@Service
public class SmInvadeDefenceDeviceServiceImpl extends ServiceImpl<SmInvadeDefenceDeviceMapper, SmInvadeDefenceDevice> implements SmInvadeDefenceDeviceService {

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public List<Device> getAllDevice() {
        List<Device> devices = deviceMapper.selectList(null);
        return devices;
    }
}
