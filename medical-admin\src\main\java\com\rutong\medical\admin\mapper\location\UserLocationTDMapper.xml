<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.location.UserLocationTDMapper">



    <insert id="cardInsert" parameterType="com.rutong.medical.admin.entity.location.UserLocation">
        INSERT INTO ${tableName}
            USING alarm_location_detail
    TAGS (#{deviceTypeCode}, #{deviceSn})
    VALUES
         (#{createTime},
            #{isAlarm},
            #{isKey},
            #{newLocatorSn},
            #{oldLocatorSn},
            #{userId},
            #{userName},
            #{employeeNumber},
            #{baseStationSn},
            #{buildingId},
            #{floorId},
            #{pointId},
            #{buildingName},
            #{floorName},
            #{pointName},
            #{alarmDetailId},
            #{alarmType},
            #{deptName},
            #{deptCode},
            #{tagCode}
        )
    </insert>

    <insert id="buttonInsert">
        INSERT INTO ${tableName} (
            create_time,
            is_alarm,
            is_key,
            new_locator_sn,
            old_locator_sn,
            base_station_sn,
            building_id,
            floor_id,
            point_id,
            building_name,
            floor_name,
            point_name,
            alarm_detail_id,
            alarm_type
        ) USING alarm_location_detail TAGS (
            #{deviceTypeCode},
            #{deviceSn}
            ) VALUES (
            #{createTime},
            #{isAlarm},
            #{isKey},
            #{newLocatorSn},
            #{oldLocatorSn},
            #{baseStationSn},
            #{buildingId},
            #{floorId},
            #{pointId},
            #{buildingName},
            #{floorName},
            #{pointName},
            #{alarmDetailId},
            #{alarmType}
            )

    </insert>

    <select id="selectUserLocationByDeviceSn"
            resultType="com.rutong.medical.admin.vo.location.UserLocationVO">
        select building_name  as buildingName,
               floor_name     as floorName,
               point_name     as pointName,
               new_locator_sn as newLocatorSn,
               old_locator_sn as oldLocatorSn
        from alarm_location_detail
        where device_sn = #{deviceSn}
        order by create_time desc LIMIT 1
    </select>

    <select id="selectMedicalByFloor" resultType="com.rutong.medical.admin.vo.location.UserLocationVO">
        select user_id as userId, user_name as userName, dept_name as deptName, device_sn as deviceSn
        from alarm_location_detail
        where floor_id = #{floorId} and tag_code = #{tagCode}
        GROUP BY user_id, user_name, dept_name, device_sn
    </select>

</mapper>
