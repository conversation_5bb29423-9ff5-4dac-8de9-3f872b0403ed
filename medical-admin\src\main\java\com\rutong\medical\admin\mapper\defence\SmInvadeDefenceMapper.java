package com.rutong.medical.admin.mapper.defence;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rutong.medical.admin.dto.defence.DefenceManageDTO;
import com.rutong.medical.admin.entity.defence.SmInvadeDefence;
import com.rutong.medical.admin.vo.defence.DetailVO;
import com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-21
 */
public interface SmInvadeDefenceMapper extends BaseMapper<SmInvadeDefence> {

    /**
     * 分页查询防区列表
     *
     * @param defenceManageDTO 查询条件
     * @return 防区列表
     */
    List<SmInvadeDefenceVO> selectDefencePage(DefenceManageDTO defenceManageDTO);


    /**
     * 防区详情
     *
     * @param defenceManageDTO
     * @return
     */
    List<DetailVO> selectDevicePage(DefenceManageDTO defenceManageDTO);

    /**
     * 解除关联
     *
     * @param defenceId 防区ID
     * @param deviceId 设备ID
     * @return 删除成功返回true，失败返回false
     */
    Boolean deleteConnect(@Param("defenceId") Long defenceId, @Param("deviceId") Long deviceId);

}
