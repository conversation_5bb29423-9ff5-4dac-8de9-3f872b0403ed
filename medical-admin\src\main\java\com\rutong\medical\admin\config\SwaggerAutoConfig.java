package com.rutong.medical.admin.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.soft.common.core.annotation.MyRequestBody;
import com.soft.common.swagger.config.SwaggerProperties;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * @ClassName SwaggerAutoConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/7/7 15:04
 * @Version 1.0
 * @Copyright © 2025 All Rights Reserved
 */
@EnableSwagger2WebMvc
@EnableKnife4j
@EnableConfigurationProperties(SwaggerProperties.class)
@ConditionalOnProperty(prefix = "swagger", name = "enabled")
public class SwaggerAutoConfig {

    @Bean
    public Docket station(SwaggerProperties properties) {
        return new Docket(DocumentationType.SWAGGER_2).groupName("01. 基站管理接口")
            .ignoredParameterTypes(MyRequestBody.class).apiInfo(apiInfo(properties)).select()
            .apis(RequestHandlerSelectors
                .basePackage(properties.getServiceBasePackage() + ".medical.admin.controller.station"))
            .paths(PathSelectors.any()).build();
    }

    @Bean
    public Docket upmsDocket(SwaggerProperties properties) {
        return new Docket(DocumentationType.SWAGGER_2).groupName("02. 系统接口").ignoredParameterTypes(MyRequestBody.class)
            .apiInfo(apiInfo(properties)).select()
            .apis(RequestHandlerSelectors
                .basePackage(properties.getServiceBasePackage() + ".medical.admin.controller.system"))
            .paths(PathSelectors.any()).build();
    }

    @Bean
    public Docket device(SwaggerProperties properties) {
        return new Docket(DocumentationType.SWAGGER_2).groupName("03. 设备管理接口")
            .ignoredParameterTypes(MyRequestBody.class).apiInfo(apiInfo(properties)).select()
            .apis(RequestHandlerSelectors
                .basePackage(properties.getServiceBasePackage() + ".medical.admin.controller.device"))
            .paths(PathSelectors.any()).build();
    }

    @Bean
    public Docket monitor(SwaggerProperties properties) {
        return new Docket(DocumentationType.SWAGGER_2).groupName("04. 视频监控管理接口")
                .ignoredParameterTypes(MyRequestBody.class).apiInfo(apiInfo(properties)).select()
                .apis(RequestHandlerSelectors
                        .basePackage(properties.getServiceBasePackage() + ".medical.admin.controller.monitor"))
                .paths(PathSelectors.any()).build();
    }

    @Bean
    public Docket location(SwaggerProperties properties) {
        return new Docket(DocumentationType.SWAGGER_2).groupName("05. 人员定位")
                .ignoredParameterTypes(MyRequestBody.class).apiInfo(apiInfo(properties)).select()
                .apis(RequestHandlerSelectors
                        .basePackage(properties.getServiceBasePackage() + ".medical.admin.controller.location"))
                .paths(PathSelectors.any()).build();
    }

    @Bean
    public Docket defence(SwaggerProperties properties) {
        return new Docket(DocumentationType.SWAGGER_2).groupName("06. 防区管理接口")
                .ignoredParameterTypes(MyRequestBody.class).apiInfo(apiInfo(properties)).select()
                .apis(RequestHandlerSelectors
                        .basePackage(properties.getServiceBasePackage() + ".medical.admin.controller.defence"))
                .paths(PathSelectors.any()).build();
    }

    @Bean
    public Docket alarm(SwaggerProperties properties) {
        return new Docket(DocumentationType.SWAGGER_2).groupName("07. 报警配置接口")
                .ignoredParameterTypes(MyRequestBody.class).apiInfo(apiInfo(properties)).select()
                .apis(RequestHandlerSelectors
                        .basePackage(properties.getServiceBasePackage() + ".medical.admin.controller.alarm"))
                .paths(PathSelectors.any()).build();
    }

    private ApiInfo apiInfo(SwaggerProperties properties) {
        return new ApiInfoBuilder().title(properties.getTitle()).description(properties.getDescription())
            .version(properties.getVersion()).build();
    }

}
