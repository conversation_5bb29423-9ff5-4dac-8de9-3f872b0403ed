package com.rutong.medical.admin.entity.defence;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Date 2025-07-19
 */
@Data
@TableName("sm_invade_defence")
public class SmInvadeDefence {
    /**
     * 防区表id
     */
    private Long id;

    /**
     * 防区编号
     */
    private String defenceCode;

    /**
     * 防区名称
     */
    private String defenceName;

    /**
     * 防区状态(0:撤防，1:布防)
     */
    private Integer defenceState;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDate createTime;

    /**
     * 更新人
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDate updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

}
