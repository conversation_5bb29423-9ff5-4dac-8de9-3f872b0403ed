<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.defence.SmInvadeDefenceMapper">

    <!-- 分页查询防区列表 -->
    <select id="selectDefencePage" parameterType="com.rutong.medical.admin.dto.defence.DefenceManageDTO"
            resultType="com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO">
        SELECT
        id,
        defence_code as defenceCode,
        defence_name as defenceName,
        defence_state as defenceState,
        start_time as startTime,
        end_time as endTime,
        create_user_id as createUserId,
        create_time as createTime,
        update_user_id as updateUserId,
        update_time as updateTime,
        is_delete as isDelete
        FROM sm_invade_defence
        <where>
            is_delete = 0
            <if test="defenceName != null and defenceName != ''">
                AND defence_name LIKE CONCAT('%', #{defenceName}, '%')
            </if>
            <if test="defenceState != null">
                AND defence_state = #{defenceState}
            </if>
        </where>
        ORDER BY create_time ASC
    </select>

    <!--    防区详情-->
    <select id="selectDevicePage" resultType="com.rutong.medical.admin.vo.defence.DetailVO">

        SELECT d.defence_code      AS defenceCode,
               d.defence_name      AS defenceName,
               dev.device_name     AS deviceName,
               dev.device_sn     AS deviceSn,
               dev.space_full_name AS spaceFullName,
        dev.id as deviceId
        FROM sm_invade_defence_device dd
                 LEFT JOIN sm_invade_defence d
                           ON dd.invade_defence_id = d.id
                 LEFT JOIN sm_device dev
                           ON dd.device_id = dev.id
        WHERE dd.invade_defence_id = #{invadeDefenceId}

    </select>


    <!--    解除关联-->
    <delete id="deleteConnect">
        DELETE
        FROM sm_invade_defence_device
        WHERE invade_defence_id = #{invadeDefenceId}
          and device_id = #{deviceId}
    </delete>


</mapper>