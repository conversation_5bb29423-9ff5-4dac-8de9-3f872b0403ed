package com.rutong.medical.admin.service.defence.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.dto.defence.DefenceAndDeviceDTO;
import com.rutong.medical.admin.dto.defence.DefenceArmDTO;
import com.rutong.medical.admin.dto.defence.DefenceManageDTO;
import com.rutong.medical.admin.dto.defence.DefenceSaveOrUpdateDTO;
import com.rutong.medical.admin.entity.defence.SmInvadeDefence;
import com.rutong.medical.admin.mapper.defence.SmInvadeDefenceMapper;
import com.rutong.medical.admin.service.defence.SmInvadeDefenceService;
import com.rutong.medical.admin.vo.defence.DetailVO;
import com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.util.MyPageUtil;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-21
 */
@Service
public class SmInvadeDefenceServiceImpl extends ServiceImpl<SmInvadeDefenceMapper, SmInvadeDefence> implements SmInvadeDefenceService {

    @Autowired
    private SmInvadeDefenceMapper smInvadeDefenceMapper;

    /**
     * 分页查询
     *
     * @param defenceManageDTO
     * @return
     */
    @Override
    public MyPageData<SmInvadeDefenceVO> page(DefenceManageDTO defenceManageDTO) {

        // 分页设置
        Integer pageNum = defenceManageDTO.getPageNum();
        Integer pageSize = defenceManageDTO.getPageSize();

        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }

        // 查询防区列表
        List<SmInvadeDefenceVO> list = smInvadeDefenceMapper.selectDefencePage(defenceManageDTO);

        // 使用MyPageUtil工具类转换为分页数据
        return MyPageUtil.makeResponseData(list);
    }

    /**
     * 新建/编辑 防区
     *
     * @param defenceSaveOrUpdateDTO
     * @return
     */
    @Override
    public boolean saveOrUpdate(DefenceSaveOrUpdateDTO defenceSaveOrUpdateDTO) {
        String defenceCode = defenceSaveOrUpdateDTO.getDefenceCode();
        String defenceName = defenceSaveOrUpdateDTO.getDefenceName();
        Long id = defenceSaveOrUpdateDTO.getId();

        SmInvadeDefence smInvadeDefence = new SmInvadeDefence();
        smInvadeDefence.setId(id);
        smInvadeDefence.setDefenceCode(defenceCode);
        smInvadeDefence.setDefenceName(defenceName);
        smInvadeDefence.setDefenceState(0);
        // 默认开始时间 20:30
        smInvadeDefence.setStartTime(LocalTime.of(20, 30));
        // 默认结束结束 8:30
        smInvadeDefence.setEndTime(LocalTime.of(8, 00));
        //  TODO 创建人
        smInvadeDefence.setCreateUserId(1000L);
        smInvadeDefence.setCreateTime(LocalDate.now());
        // TODO 更新人
        smInvadeDefence.setUpdateUserId(1000L);
        smInvadeDefence.setUpdateTime(LocalDate.now());
        // 默认撤防
        smInvadeDefence.setIsDelete(0);

        // 检查数据库中是否存在该id的记录
        if (id != null) {
            SmInvadeDefence existingRecord = smInvadeDefenceMapper.selectById(id);
            if (existingRecord != null) {
                // 如果记录存在，则更新


                return SqlHelper.retBool(smInvadeDefenceMapper.updateById(smInvadeDefence));
            }
        }

        // 如果id为null或者数据库中不存在该id的记录，则插入新记录
        // 插入时需要将id设置为null，让数据库自动生成
//        smInvadeDefence.setId(null);
        // 插入防区id
//        smInvadeDefenceMapper.insert(smInvadeDefence)

        // 插入 设备id;

        return SqlHelper.retBool(smInvadeDefenceMapper.insert(smInvadeDefence));
    }

    /**
     * 解除关联
     *
     * @param defenceAndDeviceDTO 防区设备表ID
     * @return
     */
    @Override
    public boolean removeConnect(DefenceAndDeviceDTO defenceAndDeviceDTO) {
        Long defenceId = defenceAndDeviceDTO.getInvadeDefenceId();
        Long deviceId = defenceAndDeviceDTO.getDeviceId();

        // 删除防区设备关联记录
        boolean result = smInvadeDefenceMapper.deleteConnect(defenceId,deviceId);

        return result;
    }

    /**
     * 布防/撤防/自动布防
     *
     * @param defenceArmDTO 布防操作参数
     * @return
     */
    @Override
    public Boolean updateDefenceArm(DefenceArmDTO defenceArmDTO) {
        // 参数校验
        if (CollectionUtils.isEmpty(defenceArmDTO.getDefenceIds())) {
            throw new IllegalArgumentException("防区ID列表不能为空");
        }

        if (defenceArmDTO.getOperationType() == null) {
            throw new IllegalArgumentException("操作类型不能为空");
        }

        // 根据操作类型进行不同的处理
        if (defenceArmDTO.getOperationType() == 1) {
            // 手动布防/撤防
            return handleManualArm(defenceArmDTO);
        } else if (defenceArmDTO.getOperationType() == 2) {
            // 自动布防
            return handleAutoArm(defenceArmDTO);
        } else {
            throw new IllegalArgumentException("操作类型无效，只支持1（手动布防/撤防）或2（自动布防）");
        }
    }

    /**
     * 处理手动布防/撤防
     *
     * @param defenceArmDTO
     * @return
     */
    private Boolean handleManualArm(DefenceArmDTO defenceArmDTO) {
        if (defenceArmDTO.getDefenceState() == null) {
            throw new IllegalArgumentException("手动操作时布防状态不能为空");
        }

        if (defenceArmDTO.getDefenceState() != 0 && defenceArmDTO.getDefenceState() != 1) {
            throw new IllegalArgumentException("布防状态只能是0（撤防）或1（布防）");
        }

        // 批量更新防区状态
        for (Long defenceId : defenceArmDTO.getDefenceIds()) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setDefenceState(defenceArmDTO.getDefenceState());
            defence.setUpdateTime(LocalDate.now());
            // TODO: 设置更新用户ID
            // defence.setUpdateUserId(getCurrentUserId());

            // 如果是撤防，清空自动布防时间
            if (defenceArmDTO.getDefenceState() == 0) {

                defence.setStartTime(null);
                defence.setEndTime(null);
            }

            updateById(defence);
        }

        return true;
    }

    /**
     * 处理自动布防
     *
     * @param defenceArmDTO
     * @return
     */
    private Boolean handleAutoArm(DefenceArmDTO defenceArmDTO) {
        if (defenceArmDTO.getStartTime() == null || defenceArmDTO.getEndTime() == null) {
            throw new IllegalArgumentException("自动布防时开始时间和结束时间不能为空");
        }

        // 校验时间：开始时间不能比结束时间晚
        if (defenceArmDTO.getStartTime().isAfter(defenceArmDTO.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能比结束时间晚");
        }

        // 批量设置自动布防
        for (Long defenceId : defenceArmDTO.getDefenceIds()) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setDefenceState(1); // 自动布防默认设置为布防状态
            defence.setStartTime(defenceArmDTO.getStartTime());
            defence.setEndTime(defenceArmDTO.getEndTime());
            defence.setUpdateTime(LocalDate.now());
            // TODO: 设置更新用户ID
            // defence.setUpdateUserId(getCurrentUserId());

            updateById(defence);
        }

        return true;
    }

    /**
     * 批量删除防区
     *
     * @param defenceIds 防区ID列表
     * @return
     */
    @Override
    public Boolean batchDeleteDefence(List<Long> defenceIds) {
        if (CollectionUtils.isEmpty(defenceIds)) {
            throw new IllegalArgumentException("防区ID列表不能为空");
        }

        // 软删除：将is_delete字段更新为1
        for (Long defenceId : defenceIds) {
            SmInvadeDefence defence = new SmInvadeDefence();
            defence.setId(defenceId);
            defence.setIsDelete(1); // 1表示已删除
            defence.setUpdateTime(LocalDate.now()); // 更新修改时间
            updateById(defence);
        }

        return true;
    }


    /**
     * 防区详情
     *
     * @param defenceManageDTO
     * @return
     */
    @Override
    public MyPageData<DetailVO> getAllDevice(DefenceManageDTO defenceManageDTO) {

        Integer pageNum = defenceManageDTO.getPageNum();
        Integer pageSize = defenceManageDTO.getPageSize();

        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        List<DetailVO> list = smInvadeDefenceMapper.selectDevicePage(defenceManageDTO);
        return MyPageUtil.makeResponseData(list);
    }
}
