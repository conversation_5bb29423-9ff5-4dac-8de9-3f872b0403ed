package com.rutong.medical.admin.entity.defence;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025-07-19
 */

@Data
@TableName("sm_invade_defence_device")
public class SmInvadeDefenceDevice {

    /**
     * 防区设备表id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 防区id
     */
    private Long invadeDefenceId;

    /**
     * 设备id
     */
    private Long deviceId;

}
