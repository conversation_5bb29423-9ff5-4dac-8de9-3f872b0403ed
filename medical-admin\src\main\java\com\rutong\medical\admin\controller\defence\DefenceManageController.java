package com.rutong.medical.admin.controller.defence;


import com.rutong.medical.admin.dto.defence.DefenceAndDeviceDTO;
import com.rutong.medical.admin.dto.defence.DefenceArmDTO;
import com.rutong.medical.admin.dto.defence.DefenceManageDTO;
import com.rutong.medical.admin.dto.defence.DefenceSaveOrUpdateDTO;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.service.defence.SmInvadeDefenceDeviceService;
import com.rutong.medical.admin.service.defence.SmInvadeDefenceService;
import com.rutong.medical.admin.vo.defence.DefenceManageVO;
import com.rutong.medical.admin.vo.defence.SmInvadeDefenceVO;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.soft.common.core.constant.ErrorCodeEnum.SERVER_INTERNAL_ERROR;

/**
 * 防区管理 控制类
 *
 * <AUTHOR>
 * @Date 2025-07-19
 */
@Api(tags = "防区管理")
@RestController
@RequestMapping("/defenceManage")
public class DefenceManageController {

    @Autowired
    private SmInvadeDefenceService smInvadeDefenceService;

    @Autowired
    private SmInvadeDefenceDeviceService smInvadeDefenceDeviceService;

    /**
     * 分页查询
     *
     * @param defenceManageDTO
     * @return
     */
    @ApiOperation(value = "防区分页查询")
    @GetMapping("/page")
    public ResponseResult<MyPageData<SmInvadeDefenceVO>> page(DefenceManageDTO defenceManageDTO) {
        return ResponseResult.success(smInvadeDefenceService.page(defenceManageDTO));
    }

    /**
     * 新建/编辑
     *
     * @param defenceSaveOrUpdateDTO
     * @return
     */
    @ApiOperation(value = "新建/编辑")
    @PostMapping("/saveOrUpdate")
    public ResponseResult<Void> defenceSaveOrUpdate(@RequestBody DefenceSaveOrUpdateDTO defenceSaveOrUpdateDTO) {
        boolean result = smInvadeDefenceService.saveOrUpdate(defenceSaveOrUpdateDTO);
        return result ? ResponseResult.success() : ResponseResult.error(SERVER_INTERNAL_ERROR);
    }

    // 设备选择
    @ApiOperation(value = "选择设备")
    @GetMapping("/getAllDevice")
    public ResponseResult<List<Device>> getAllDevice() {
        List<Device> getAllDevice = smInvadeDefenceDeviceService.getAllDevice();
        return ResponseResult.success(getAllDevice);
    }

    /**
     * 解除关联
     *
     * @param defenceAndDeviceDTO
     * @return
     */
    @ApiOperation(value = "解除关联")
    @PostMapping("/remove")
    public ResponseResult<Void> dissolve(@RequestBody DefenceAndDeviceDTO defenceAndDeviceDTO) {
        boolean removeById = smInvadeDefenceService.removeConnect(defenceAndDeviceDTO);
        return removeById ? ResponseResult.success() : ResponseResult.error(SERVER_INTERNAL_ERROR);
    }

    /**
     * 布防/撤防
     *
     * @param defenceArmDTO
     * @return
     */
    @ApiOperation(value = "布防/撤防/自动布防", notes = "支持批量操作，操作类型：1-手动布防/撤防，2-自动布防")
    @PostMapping("/arm")
    public ResponseResult<Boolean> updateDefenceArm(@RequestBody DefenceArmDTO defenceArmDTO) {
        Boolean result = smInvadeDefenceService.updateDefenceArm(defenceArmDTO);
        return ResponseResult.success(result);
    }

    /**
     * 删除防区
     *
     * @param defenceIds
     * @return
     */
    @ApiOperation(value = "删除防区", notes = "根据防区ID列表删除防区")
    @PostMapping("/batchDelete")
    public ResponseResult<Boolean> batchDeleteDefence(@RequestBody List<Long> defenceIds) {
        Boolean result = smInvadeDefenceService.batchDeleteDefence(defenceIds);
        return ResponseResult.success(result);
    }

    /**
     * 详情
     *
     * @param defenceManageDTO
     * @return
     */
    @ApiOperation(value = "详情")
    @PostMapping("/detail")
    public ResponseResult<MyPageData<DefenceManageVO>> detail(@RequestBody DefenceManageDTO defenceManageDTO) {
        MyPageData<DefenceManageVO> allDevice = smInvadeDefenceService.getAllDevice(defenceManageDTO);
        return ResponseResult.success(allDevice);
    }
}
